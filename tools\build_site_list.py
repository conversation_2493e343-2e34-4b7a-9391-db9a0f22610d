import re
from pathlib import Path
from urllib.parse import urlparse

ROOT = Path(r"C:\\Cursor_Project")
SRC = ROOT / "list.md"
OUT = ROOT / "websites_dedup.md"

md_link_re = re.compile(r"\[[^\]]*\]\((https?://[^\)]+)\)", re.I)
domain_re = re.compile(r"([a-z0-9][-a-z0-9\.]*\.[a-z]{2,})(?![a-zA-Z0-9])", re.I)

# Load lines
lines = SRC.read_text(encoding='utf-8', errors='ignore').splitlines()

# Extract candidate domains
candidates = []  # list of (domain, source_line)
for line in lines:
    s = line.strip()
    if not s:
        continue
    # Remove obvious subcategory-only lines without any domain and with colon
    # We'll still try to pick domain if present
    dom = None
    m = md_link_re.search(s)
    if m:
        try:
            url = m.group(1)
            netloc = urlparse(url).netloc.lower()
            if netloc:
                dom = netloc
        except Exception:
            pass
    if not dom:
        m2 = domain_re.search(s)
        if m2:
            dom = m2.group(1).lower()
    if dom:
        # normalize leading www.
        dom = dom.strip()
        # strip trailing punctuation
        dom = dom.strip(').,;:')
        candidates.append((dom, line))

# Build canonical set with subdomain reduction
# Step 1: prefer without leading www. for matching, but keep shortest display form
by_key = {}  # key -> display_domain
for dom, _ in candidates:
    key = dom[4:] if dom.startswith('www.') else dom
    cur = by_key.get(key)
    # choose shorter display form
    if cur is None or len(dom) < len(cur):
        by_key[key] = dom

# Step 2: collapse subdomains: if a key is a subdomain of another key, keep the shorter parent
keys = sorted(by_key.keys(), key=lambda k: (k.count('.'), len(k)))
final_keys = []
for k in keys:
    # if any existing final key is a suffix of this key, then this k is subdomain of existing parent; skip it
    is_sub = False
    for fk in final_keys:
        if k == fk:
            is_sub = True
            break
        if k.endswith('.' + fk):
            # e.g., video.sky.it endswith sky.it -> subdomain
            is_sub = True
            break
    if not is_sub:
        final_keys.append(k)

# Compose output list using display domains resolved in by_key
final_domains = [by_key[k] for k in final_keys]
final_domains = sorted(set(final_domains))

# Write output as Markdown list
with OUT.open('w', encoding='utf-8', errors='ignore') as f:
    f.write("# Websites (Deduplicated)\n\n")
    for d in final_domains:
        url = 'https://' + d if not d.startswith('http') else d
        f.write(f"- [{d}]({url})\n")

print("DONE", {"sites": len(final_domains), "out": str(OUT)})
