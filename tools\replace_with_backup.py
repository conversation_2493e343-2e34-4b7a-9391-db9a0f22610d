import shutil, time
from pathlib import Path

ROOT = Path(r"C:\\Cursor_Project")
SRC = ROOT / "list.linked.md"
DST = ROOT / "list.md"
TS = time.strftime("%Y%m%d-%H%M%S")
BAK = ROOT / f"list.md.bak-{TS}"

# sanity: ensure src exists and has content
if not SRC.exists() or SRC.stat().st_size == 0:
    raise SystemExit("ERROR: linked file missing or empty")

# backup original
shutil.copyfile(DST, BAK)
# replace
shutil.copyfile(SRC, DST)
print("REPLACED", str(DST), "BACKUP", str(BAK))
