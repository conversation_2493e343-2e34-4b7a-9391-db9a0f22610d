import re
from pathlib import Path
from urllib.parse import urlparse
import tldextract

ROOT = Path(r"C:\\Cursor_Project")
SRC = ROOT / "list.md"
OUT = ROOT / "websites_dedup.md"

md_link_re = re.compile(r"\[[^\]]*\]\((https?://[^\)]+)\)", re.I)
domain_re = re.compile(r"([a-z0-9][-a-z0-9\.]*\.[a-z]{2,})(?![a-zA-Z0-9])", re.I)

lines = SRC.read_text(encoding='utf-8', errors='ignore').splitlines()

cands = set()
for line in lines:
    s = line.strip()
    if not s:
        continue
    dom = None
    m = md_link_re.search(s)
    if m:
        try:
            url = m.group(1)
            netloc = urlparse(url).netloc.lower()
            if netloc:
                dom = netloc
        except Exception:
            pass
    if not dom:
        m2 = domain_re.search(s)
        if m2:
            dom = m2.group(1).lower()
    if not dom:
        continue
    # extract eTLD+1
    ext = tldextract.extract(dom)
    if not ext.domain or not ext.suffix:
        continue
    reg = f"{ext.domain}.{ext.suffix}"
    cands.add(reg)

# normalize and sort
final = sorted(cands)

with OUT.open('w', encoding='utf-8', errors='ignore') as f:
    f.write("# Websites (Deduplicated, eTLD+1)\n\n")
    for d in final:
        f.write(f"- [{d}](https://{d})\n")

print("DONE", {"sites": len(final), "out": str(OUT)})
