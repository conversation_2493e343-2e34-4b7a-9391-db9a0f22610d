import re, shutil, sys
from pathlib import Path

SRC = Path(r"C:\\Cursor_Project\\list.md")
BACKUP = SRC.with_suffix('.backup.md')

# Backup
shutil.copyfile(SRC, BACKUP)

with SRC.open('r', encoding='utf-8', errors='ignore') as f:
    lines = f.readlines()

seed = {
    '10play': '10play.com.au',
    '7plus': '7plus.com.au',
    '9now.com.au': '9now.com.au',
    '1news': '1news.co.nz',
    'abc.net.au': 'abc.net.au',
    'bbc': 'bbc.co.uk',
    'bbc.co.uk': 'bbc.co.uk',
    '9gag': '9gag.com',
    'cbsnews': 'cbsnews.com',
    'cbc.ca': 'cbc.ca',
    'faz.net': 'faz.net',
    'foxnews': 'foxnews.com',
    'francetv': 'france.tv',
    'fptplay': 'fptplay.vn',
    'goplay': 'goplay.be',
    'globo': 'globo.com',
    'hbo': 'www.hbo.com',
    'ign.com': 'ign.com',
    'instagram': 'instagram.com',
    'iq.com': 'www.iq.com',
    'iqiyi': 'www.iqiyi.com',
    'jtbc': 'jtbc.co.kr',
    'khanacademy': 'www.khanacademy.org',
    'likee': 'likee.video',
    'linkedin': 'www.linkedin.com',
    'loom': 'www.loom.com',
    'mailru': 'mail.ru',
    'media.ccc.de': 'media.ccc.de',
    'microsoft': 'www.microsoft.com',
    'msn': 'www.msn.com',
    'nbc': 'www.nbc.com',
    'ndr': 'www.ndr.de',
    'netease': 'music.163.com',
    'newgrounds': 'www.newgrounds.com',
    'nytimes': 'www.nytimes.com',
    'ocw.mit.edu': 'ocw.mit.edu',
    'openrec': 'www.openrec.tv',
    'paramountplus': 'www.paramountplus.com',
    'pbs': 'www.pbs.org',
    'pearvideo': 'www.pearvideo.com',
    'pinterest': 'www.pinterest.com',
    'pornhub': 'www.pornhub.com',
    'reddit': 'www.reddit.com',
    'rte': 'www.rte.ie',
    'rtve.es': 'www.rtve.es',
    'rumble': 'rumble.com',
    'sbs.com.au': 'www.sbs.com.au',
    'sky.it': 'www.sky.it',
    'soundcloud': 'soundcloud.com',
    'spotify': 'www.spotify.com',
    'store.steampowered.com': 'store.steampowered.com',
    'tiktok': 'www.tiktok.com',
    'twitch': 'www.twitch.tv',
    'twitter': 'twitter.com',
    'udemy': 'www.udemy.com',
    'vimeo': 'vimeo.com',
    'vk': 'vk.com',
    'youtube': 'www.youtube.com',
    'youtu.be': 'youtu.be',
    'zdf': 'www.zdf.de',
}

domain_re = re.compile(r"([a-z0-9][-a-z0-9\.]*\.[a-z]{2,})(?![a-zA-Z0-9])", re.I)


def pick_domain(line: str):
    m = domain_re.search(line)
    if m:
        return m.group(1)
    tokens = re.split(r"[^a-z0-9]+", line.lower())
    for t in tokens:
        if not t:
            continue
        if t in seed:
            return seed[t]
    return None


def transform(line: str):
    text = line.rstrip('\n')
    if not text.strip():
        return line
    dom = pick_domain(text)
    if not dom:
        return line
    url = 'https://' + dom if not dom.startswith('http') else dom
    # if already Markdown link, keep
    if '[' in text and ']' in text and '(' in text and ')' in text:
        return line
    stripped = text.strip()
    if stripped.lower() == dom.lower():
        return f"[{stripped}]({url})\n"
    if dom in text:
        return text.replace(dom, f"[{dom}]({url})") + "\n"
    return f"{text} — [{dom}]({url})\n"

out_lines = [transform(ln) for ln in lines]

with SRC.open('w', encoding='utf-8', errors='ignore') as f:
    f.writelines(out_lines)

print("DONE", {
    'input_lines': len(lines),
    'output_lines': len(out_lines),
    'backup': str(BACKUP),
})
