# 文章创作执行计划

## 用户需求和目标
- **文章主题**: Download Christmas Songs
- **SEO关键词**: download christmas songs
- **目标字数**: 1600字（最多可超出20%，即最高1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: B (修辞问题开头)
- **推荐产品**: Cinch Audio Recorder

## 需要执行的详细步骤清单

### 第1步：提取参考URL内容
- [ ] 抓取并分析3个参考URL的内容结构
- [ ] 提取H2-H4级标题
- [ ] 识别内容空白和改进机会

### 第2步：生成超级大纲
- [ ] 合并整理提取的标题
- [ ] 按层级结构重新组织
- [ ] 保存至 `super_outline.md`

### 第3步：创建最终大纲
- [ ] 基于竞品分析优化大纲结构
- [ ] 添加独特价值点和人工经验要素
- [ ] 进行智能字数分配
- [ ] 保存至 `final_outline.md`

### 第4步：撰写初稿
- [ ] 按照最终大纲撰写文章
- [ ] 确保符合拟人化写作要求
- [ ] 整合Cinch Audio Recorder产品推荐
- [ ] 保存至 `first_draft.md`

### 第5步：生成SEO内容
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image提示词
- [ ] 保存至 `seo_metadata_images.md`

### 第6步：质量检查
- [ ] 验证字数是否在1600-1920字范围内
- [ ] 检查拟人化写作风格
- [ ] 验证链接有效性
- [ ] 确认图片添加

## 完成标准和检查点

### 内容质量标准
- **Effort**: 体现明显的人工成分和深度思考
- **Originality**: 提供3-5个独特观点或解决方案
- **Talent/Skill**: 展示专业知识和实际经验
- **Accuracy**: 确保事实准确性

### 信息增量要求
- 至少3-5个其他文章未涵盖的独特观点
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案

### 字数分配目标（基于1600字）
- Introduction: 128字 (8%)
- 核心推荐章节(Cinch Audio Recorder): 320-400字 (20-25%)
- 主要内容章节: 560-640字 (35-40%)
- 支撑章节: 400-480字 (25-30%)
- Conclusion + FAQ: 192字 (12%)

## 预期输出文件清单
1. `plan.md` - 执行计划文件 ✓
2. `super_outline.md` - 初级大纲
3. `final_outline.md` - 最终优化大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO内容和图片提示词

## 参考资源
- 用户要求文件: `New_article/info_aia.md`
- 产品指南: `New_article/car_guide.md`
- 拟人化写作指南: `New_article/hl.md`
- 参考URL:
  - https://www.tunecable.com/topics/free-download-christmas-songs.html
  - https://www.noteburner.com/youtube-music-tips/free-download-christmas-music-from-youtube.html
  - https://hub.mrc.fm/c/podcasting-tips/how-to-download-christmas-songs-as-mp3-files-a-step-by-step-guide
