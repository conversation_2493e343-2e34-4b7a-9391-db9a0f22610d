# SoundCloud on Sonos Article Creation Plan

## User Requirements Summary
- **Topic**: SoundCloud on Sonos
- **SEO Keywords**: SoundCloud on Sonos
- **Article Length**: 1600 Words (minimum, can exceed by up to 20%)
- **Language**: English
- **Timeframe**: June 2025 content with latest data and trends
- **Audience**: Music lovers and creators focused on downloading, editing, and sharing music
- **Opening Strategy**: B (Rhetorical Question Opening)
- **Recommended Product**: Cinch Audio Recorder Pro

## Reference URLs for Research
1. https://www.drmare.com/soundcloud-music/play-soundcloud-on-sonos.html
2. https://help.soundcloud.com/hc/en-us/articles/20543636025243-SoundCloud-on-Sonos
3. https://www.tuneskit.com/record-audio/play-soundcloud-on-sonos.html
4. https://www.noteburner.com/topic-tips/play-soundcloud-on-sonos.html
5. https://www.drmare.com/audio-recorder/play-soundcloud-on-sonos.html
6. https://www.viwizard.com/record-audio/play-soundcloud-on-sonos.html

## Content Quality Requirements
### Four Quality Assessment Dimensions
- **Effort**: Must show obvious human elements and deep thinking
- **Originality**: Provide unique information increment, avoid rehashing existing content
- **Talent/Skill**: Demonstrate author's professional knowledge and practical experience
- **Accuracy**: Ensure factual accuracy, avoid misinformation

### Information Increment Requirements
- Include at least 3-5 unique viewpoints or solutions not covered by other articles
- Personal insights and trial-and-error stories based on actual usage experience
- Specific solutions for user pain points, not generic advice

## Execution Steps Checklist

### Step 1: Extract User Requirements ✅
- [x] Read and extract all requirements from info_aia.md
- [x] Record requirements clearly to guide subsequent steps
- [x] Confirm all requirements understood and incorporated into plan

### Step 2: Generate Outline
- [ ] Create super outline by extracting H2-H4 headings from reference URLs
- [ ] Merge and organize extracted headings
- [ ] Save initial outline as `super_outline.md`
- [ ] Optimize outline based on user requirements and research
- [ ] Save final outline as `final_outline.md`

### Step 3: Create First Draft
- [ ] Use final outline to write first draft following `first_draft.md` workflow
- [ ] Follow humanized writing style from `hl.md`
- [ ] Integrate Cinch Audio Recorder Pro recommendations per `car_guide.md`
- [ ] Save first draft as `first_draft.md`

### Step 4: Generate SEO Content
- [ ] Follow `seo_titles.md` workflow
- [ ] Create 5 SEO title and meta description combinations
- [ ] Generate featured image prompt
- [ ] Save all SEO content to `seo_metadata_images.md`

### Step 5: Quality Check
- [ ] Verify word count is within required range (1600+ words, max 20% over)
- [ ] Check each paragraph follows humanized writing requirements from `hl.md`
- [ ] Identify and eliminate obvious AI language and sentence structures
- [ ] Verify internal and external links are added per requirements and check link validity
- [ ] Confirm relevant images are added for each H2 section

## Expected Output Files
1. `/{keyword}/plan.md` - This execution plan
2. `/{keyword}/super_outline.md` - Initial outline from reference URL analysis
3. `/{keyword}/final_outline.md` - Optimized final outline with word count allocations
4. `/{keyword}/first_draft.md` - Complete article first draft
5. `/{keyword}/seo_metadata_images.md` - SEO titles, meta descriptions, and image prompts

## Success Criteria
- Article reaches 1600-1800 words
- Contains 3-5 unique insights not found in competitor articles
- Includes personal experience/trial-and-error stories
- Proper product integration with download links
- SEO keyword distribution throughout content
- Rich content elements (tables, lists, etc.)
- Specific relevant images for each H2 section
- All links validated and working
- Humanized writing style throughout
