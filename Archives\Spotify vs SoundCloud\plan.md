# Spotify vs SoundCloud 文章创作执行计划

## 用户需求和目标
- **文章主题**: Spotify vs SoundCloud
- **目标字数**: 1600字（最多可超出20%，即最高1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **推荐产品**: Cinch Audio Recorder
- **开头策略**: A（惊人统计/事实开头）

## 需要执行的详细步骤清单

### 第1步：提取用户需求 ✅
- [x] 阅读并分析 info_aia.md 中的所有要求
- [x] 确认文章主题、字数、语言、受众等关键信息
- [x] 理解推荐产品和写作指导原则

### 第2步：生成文章大纲
- [ ] 分析竞品文章内容，提取H2-H4标题
- [ ] 创建超级大纲（super_outline.md）
- [ ] 优化并生成最终大纲（final_outline.md）
- [ ] 进行竞品内容空白分析
- [ ] 挖掘独特价值点
- [ ] 分配各章节字数

### 第3步：创作文章初稿
- [ ] 根据最终大纲撰写初稿
- [ ] 遵循拟人化写作指南（hl.md）
- [ ] 整合SEO长尾关键词
- [ ] 实施Google E-E-A-T标准
- [ ] 添加产品推荐内容
- [ ] 为H2章节添加相关图片
- [ ] 添加内部和外部链接
- [ ] 丰富内容元素（表格、列表等）

### 第4步：生成SEO内容
- [ ] 创建5组SEO标题和元描述
- [ ] 生成特色图片提示词
- [ ] 保存至seo_metadata_images.md

### 第5步：质量检查
- [ ] 验证字数是否在1600-1920字范围内
- [ ] 检查拟人化写作风格
- [ ] 识别并修正AI语言痕迹
- [ ] 验证链接有效性
- [ ] 确认图片添加情况

## 完成标准和检查点

### 内容质量标准
- 包含至少3-5个独特观点或解决方案
- 体现明显的人工成分和深度思考
- 基于实际使用经验的个人见解
- 针对用户痛点的具体解决方案

### 技术要求检查
- 字数控制在1600-1920字
- 遵循拟人化写作风格
- 包含丰富的内容元素
- 正确整合推荐产品
- 添加有效的内外链接

## 预期输出文件清单
1. `plan.md` - 执行计划文件 ✅
2. `super_outline.md` - 初始超级大纲
3. `final_outline.md` - 最终优化大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO标题、描述和图片提示词

## 关键成功因素
- 严格遵循字数限制
- 体现人工经验和专业判断
- 自然整合推荐产品
- 提供独特价值和信息增量
- 保持友好实用的写作语气