import re, shutil, sys, time
from pathlib import Path

ROOT = Path(r"C:\\Cursor_Project")
SRC = ROOT / "list.md"
TMP = ROOT / "list.linked.md"
TS = time.strftime("%Y%m%d-%H%M%S")
BAK = ROOT / f"list.md.bak-{TS}"

with SRC.open('r', encoding='utf-8', errors='ignore') as f:
    lines = f.readlines()

# sanity check
if len(lines) < 1000:
    print("ABORT: unexpected small file (lines)", len(lines))
    sys.exit(2)

# high-confidence seed map (limited to avoid false positives)
seed = {
    '10play': '10play.com.au',
    '7plus': '7plus.com.au',
    '1news': 'www.1news.co.nz',
    '9gag': '9gag.com',
    'abc.net.au': 'www.abc.net.au',
    'bbc': 'www.bbc.co.uk',
    'bbc.co.uk': 'www.bbc.co.uk',
    'cbc.ca': 'www.cbc.ca',
    'cbsnews': 'www.cbsnews.com',
    'faz.net': 'www.faz.net',
    'foxnews': 'www.foxnews.com',
    'francetv': 'www.france.tv',
    'fptplay': 'fptplay.vn',
    'globo': 'www.globo.com',
    'hbo': 'www.hbo.com',
    'ign.com': 'www.ign.com',
    'instagram': 'www.instagram.com',
    'iq.com': 'www.iq.com',
    'iqiyi': 'www.iqiyi.com',
    'jtbc': 'jtbc.co.kr',
    'khanacademy': 'www.khanacademy.org',
    'likee': 'likee.video',
    'linkedin': 'www.linkedin.com',
    'loom': 'www.loom.com',
    'mailru': 'mail.ru',
    'media.ccc.de': 'media.ccc.de',
    'ndr': 'www.ndr.de',
    'newgrounds': 'www.newgrounds.com',
    'nytimes': 'www.nytimes.com',
    'ocw.mit.edu': 'ocw.mit.edu',
    'openrec': 'www.openrec.tv',
    'paramountplus': 'www.paramountplus.com',
    'pbs': 'www.pbs.org',
    'pearvideo': 'www.pearvideo.com',
    'pinterest': 'www.pinterest.com',
    'pornhub': 'www.pornhub.com',
    'reddit': 'www.reddit.com',
    'rte': 'www.rte.ie',
    'rtve.es': 'www.rtve.es',
    'rumble': 'rumble.com',
    'sbs.com.au': 'www.sbs.com.au',
    'sky.it': 'www.sky.it',
    'soundcloud': 'soundcloud.com',
    'spotify': 'www.spotify.com',
    'store.steampowered.com': 'store.steampowered.com',
    'tiktok': 'www.tiktok.com',
    'twitch': 'www.twitch.tv',
    'twitter': 'twitter.com',
    'udemy': 'www.udemy.com',
    'vimeo': 'vimeo.com',
    'vk': 'vk.com',
    'youtube': 'www.youtube.com',
    'youtu.be': 'youtu.be',
    'zdf': 'www.zdf.de',
}

domain_re = re.compile(r"([a-z0-9][-a-z0-9\.]*\.[a-z]{2,})(?![a-zA-Z0-9])", re.I)


def pick_domain(line: str):
    m = domain_re.search(line)
    if m:
        return m.group(1)
    tokens = re.split(r"[^a-z0-9]+", line.lower())
    for t in tokens:
        if not t:
            continue
        if t in seed:
            return seed[t]
    return None


def transform(line: str):
    text = line.rstrip('\n')
    if not text.strip():
        return line
    # do not touch if already has markdown link
    if '[' in text and ']' in text and '(' in text and ')' in text:
        return line
    dom = pick_domain(text)
    if not dom:
        return line
    url = 'https://' + dom if not dom.startswith('http') else dom
    stripped = text.strip()
    if stripped.lower() == dom.lower():
        return f"[{stripped}]({url})\n"
    if dom in text:
        return text.replace(dom, f"[{dom}]({url})") + "\n"
    return f"{text} — [{dom}]({url})\n"

out_lines = [transform(ln) for ln in lines]

with TMP.open('w', encoding='utf-8', errors='ignore') as f:
    f.writelines(out_lines)

print("WROTE", str(TMP), "lines:", len(out_lines))
